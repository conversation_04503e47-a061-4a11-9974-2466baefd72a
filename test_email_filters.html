<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件过滤器测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>邮件过滤器功能测试</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>新增功能测试</h5>
            </div>
            <div class="card-body">
                <!-- 收件文件夹过滤器 -->
                <div class="form-group">
                    <label for="folderFilter">收件文件夹过滤：</label>
                    <select class="form-control" id="folderFilter">
                        <option value="">选择文件夹...</option>
                        <option value="inbox">收件箱</option>
                        <option value="sent">发件箱</option>
                        <option value="draft">草稿箱</option>
                    </select>
                </div>
                
                <!-- 快速过滤按钮 -->
                <div class="form-group">
                    <label>收件文件夹快速过滤：</label>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" data-filter="folder_type" data-value="inbox">
                            <i class="fa fa-inbox mr-1"></i>收件箱
                        </button>
                        <button type="button" class="btn btn-outline-success" data-filter="folder_type" data-value="sent">
                            <i class="fa fa-paper-plane mr-1"></i>发件箱
                        </button>
                        <button type="button" class="btn btn-outline-warning" data-filter="folder_type" data-value="draft">
                            <i class="fa fa-edit mr-1"></i>草稿箱
                        </button>
                    </div>
                </div>
                
                <!-- 预设过滤器 -->
                <div class="form-group">
                    <label>预设过滤器：</label>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-info" id="customerEmailPreset">
                            <i class="fa fa-user mr-1"></i>客户邮件
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="unfollowedPreset">
                            <i class="fa fa-flag mr-1"></i>未跟进
                        </button>
                    </div>
                </div>
                
                <!-- 测试结果显示 -->
                <div class="form-group">
                    <label>测试结果：</label>
                    <div id="testResult" class="alert alert-info">
                        点击上面的按钮进行测试
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 收件文件夹下拉选择器测试
            $('#folderFilter').change(function() {
                const value = $(this).val();
                const text = $(this).find('option:selected').text();
                $('#testResult').html(`<strong>收件文件夹过滤器：</strong> ${text} (${value})`);
            });
            
            // 快速过滤按钮测试
            $('[data-filter="folder_type"]').click(function() {
                const filter = $(this).data('filter');
                const value = $(this).data('value');
                const text = $(this).text().trim();
                
                // 切换按钮状态
                $('[data-filter="folder_type"]').removeClass('btn-primary btn-success btn-warning').addClass('btn-outline-primary btn-outline-success btn-outline-warning');
                $(this).removeClass('btn-outline-primary btn-outline-success btn-outline-warning');
                
                if (value === 'inbox') {
                    $(this).addClass('btn-primary');
                } else if (value === 'sent') {
                    $(this).addClass('btn-success');
                } else if (value === 'draft') {
                    $(this).addClass('btn-warning');
                }
                
                $('#testResult').html(`<strong>快速过滤：</strong> ${text} (${filter}=${value})`);
            });
            
            // 预设过滤器测试
            $('#customerEmailPreset').click(function() {
                $(this).toggleClass('btn-outline-info btn-info');
                $('#testResult').html('<strong>预设过滤器：</strong> 客户邮件 (is_customer_email=true)');
            });
            
            $('#unfollowedPreset').click(function() {
                $(this).toggleClass('btn-outline-secondary btn-secondary');
                $('#testResult').html('<strong>预设过滤器：</strong> 未跟进 (follow_status=not_followed)');
            });
        });
    </script>
</body>
</html>
